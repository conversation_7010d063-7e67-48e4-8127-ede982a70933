<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7123b025-5878-427c-a58b-5e53d14dd314" name="Changes" comment="更新 readme">
      <change afterPath="$PROJECT_DIR$/infocollect/test/milvus_lite_example.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/app/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/app/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/intelligent_executor/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/core/executor.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/intelligent_executor/core/executor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/models/all-MiniLM-L6-v2/config.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/models/all-MiniLM-L6-v2/vocab.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/test_local_setup.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/utils/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/intelligent_executor/utils/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/utils/config_manager.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/intelligent_executor/utils/session_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/intelligent_executor/utils/session_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/requirements.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/services/ai_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/services/ai_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/services/proxy_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/services/proxy_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/services/testcase_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/services/testcase_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/templates/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/infocollect/templates/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/templates/static/css/app.7c4ce560.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/templates/static/js/app.0c489cf2.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/templates/static/js/app.0c489cf2.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/test/test.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/infocollect/test/test_milvus_lite_vector_db.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/infocollect/routes/tools_routes.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/infocollect/services/tools_service.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2x1TB1eSKu6g4TzfZ8uDG3xHbDG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/_Projects_python/Tools/console-vue2",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "sqlite"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\_Projects_python\Tools\galio\infocollect\test" />
      <recent name="D:\_Projects_python\Tools\galio\infocollect\routes\smart" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="debug" type="PythonConfigurationType" factoryName="Python">
      <module name="galio" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\Program Files\Python312\python.exe" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/infocollect" />
      <option name="PARAMETERS" value="-s" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7123b025-5878-427c-a58b-5e53d14dd314" name="Changes" comment="" />
      <created>1747104443599</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747104443599</updated>
      <workItem from="1747104446663" duration="132000" />
      <workItem from="1747104609399" duration="39121000" />
      <workItem from="1747307777930" duration="48656000" />
      <workItem from="1748245253482" duration="1423000" />
      <workItem from="1748246729504" duration="35000" />
      <workItem from="1748246950316" duration="23239000" />
      <workItem from="1748912575506" duration="9558000" />
      <workItem from="1749024914578" duration="10812000" />
      <workItem from="1749453420235" duration="21082000" />
      <workItem from="1749699055114" duration="47738000" />
      <workItem from="1750147828817" duration="2554000" />
      <workItem from="1750305032998" duration="37048000" />
      <workItem from="1750987169053" duration="1249000" />
      <workItem from="1751013689215" duration="44376000" />
    </task>
    <task id="LOCAL-00001" summary="一键式spider，预置release.zip">
      <created>1747385800446</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747385800446</updated>
    </task>
    <task id="LOCAL-00002" summary="一键式spider，预置release.zip">
      <created>1747386340661</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747386340661</updated>
    </task>
    <task id="LOCAL-00003" summary="修改bug">
      <created>1747626104057</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747626104057</updated>
    </task>
    <task id="LOCAL-00004" summary="加载测试用例，dark下显示问题">
      <created>1747905351984</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747905351984</updated>
    </task>
    <task id="LOCAL-00005" summary="add">
      <created>1748241048793</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748241048793</updated>
    </task>
    <task id="LOCAL-00006" summary="fix bug">
      <created>*************</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00007" summary="fix bug">
      <created>*************</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00008" summary="获取serviceaccount权限">
      <created>*************</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00009" summary="集成代码仓配置">
      <created>*************</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00010" summary="bug修复">
      <created>*************</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00011" summary="线程数优化、task提供详细error info">
      <created>*************</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00012" summary="优化">
      <created>1749523221457</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1749523221457</updated>
    </task>
    <task id="LOCAL-00013" summary="优化">
      <created>1749535849389</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1749535849389</updated>
    </task>
    <task id="LOCAL-00014" summary="bug fixed">
      <created>1750305343270</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1750305343270</updated>
    </task>
    <task id="LOCAL-00015" summary="临时屏蔽部分未上线功能">
      <created>1750315722360</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1750315722360</updated>
    </task>
    <task id="LOCAL-00016" summary="修复部分k8s环境登陆不加载kubectl">
      <created>1750402399090</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750402399091</updated>
    </task>
    <task id="LOCAL-00017" summary="agent log">
      <created>1750923563384</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750923563384</updated>
    </task>
    <task id="LOCAL-00018" summary="fix bug">
      <created>1751248855781</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1751248855781</updated>
    </task>
    <task id="LOCAL-00019" summary="fix bug">
      <created>1751248912794</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1751248912794</updated>
    </task>
    <task id="LOCAL-00020" summary="更新 readme">
      <created>1751339257140</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751339257140</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="一键式spider，预置release.zip" />
    <MESSAGE value="修改bug" />
    <MESSAGE value="加载测试用例，dark下显示问题" />
    <MESSAGE value="add" />
    <MESSAGE value="获取serviceaccount权限" />
    <MESSAGE value="集成代码仓配置" />
    <MESSAGE value="bug修复" />
    <MESSAGE value="线程数优化、task提供详细error info" />
    <MESSAGE value="优化" />
    <MESSAGE value="bug fixed" />
    <MESSAGE value="临时屏蔽部分未上线功能" />
    <MESSAGE value="修复部分k8s环境登陆不加载kubectl" />
    <MESSAGE value="agent log" />
    <MESSAGE value="fix bug" />
    <MESSAGE value="更新 readme" />
    <option name="LAST_COMMIT_MESSAGE" value="更新 readme" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/galio$debug.coverage" NAME="debug Coverage Results" MODIFIED="1749542529687" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>